import React, { useState, useEffect } from "react";
import { Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import NewIncidentDialog from "@/components/NewIncidentDialog";
import IncidentInvestigationView from "@/components/IncidentInvestigationView";
import IncidentActionDialog from "@/components/IncidentActionDialog";
import InvestigationStatusDialog from "@/components/InvestigationStatusDialog";
import DataTable from "@/components/DataTable";
import AllIncidentsTable from "@/components/AllIncidentsTable";
import { useUser } from "@/contexts/UserContext";
import { useIncidents, Incident } from "@/contexts/IncidentContext";
import { defaultPath } from "@/lib/paths";
import { toast } from "sonner";

const ReporterDashboard = () => {
  const { userName, userTitle } = useUser();
  const { incidents, addIncident, updateIncident, deleteIncident, getIncidentsByReporter, getIncidentsRequiringAction, getReporterActionIncidents } = useIncidents();
  const [isNewIncidentDialogOpen, setIsNewIncidentDialogOpen] = useState(false);
  const [selectedIncident, setSelectedIncident] = useState<Incident | null>(null);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isActionDialogOpen, setIsActionDialogOpen] = useState(false);
  const [isInvestigationStatusDialogOpen, setIsInvestigationStatusDialogOpen] = useState(false);
  const [reporterActionIncidents, setReporterActionIncidents] = useState<Incident[]>([]);
  const [isLoadingActions, setIsLoadingActions] = useState(false);

  // Get incidents reported by the current user
  const myReportedIncidents = getIncidentsByReporter(userName);
  console.log("Reporter userName:", userName);
  console.log("My Reported Incidents:", myReportedIncidents);

  // Get incidents that require action from the current user (fallback)
  const myActionIncidents = getIncidentsRequiringAction(userName, 'reporter');
  console.log("My Action Incidents:", myActionIncidents);

  // Load reporter action incidents from API
  useEffect(() => {
    const loadReporterActions = async () => {
      setIsLoadingActions(true);
      try {
        console.log('🔄 Loading reporter action incidents from API...');
        const apiIncidents = await getReporterActionIncidents();
        setReporterActionIncidents(apiIncidents);
        console.log(`✅ Loaded ${apiIncidents.length} reporter action incidents`);
      } catch (error) {
        console.error('❌ Error loading reporter action incidents:', error);
        // Fallback to context data if API fails
        setReporterActionIncidents(myActionIncidents);
        toast.error('Failed to load action incidents from API, using cached data');
      } finally {
        setIsLoadingActions(false);
      }
    };

    loadReporterActions();
  }, [getReporterActionIncidents]); // Now safe to include since it's memoized with useCallback

  // All incidents for reference
  const allIncidents = incidents;
  console.log("All Incidents:", allIncidents);

  const handleView = (incident: Incident) => {
    setSelectedIncident(incident);
    setIsViewDialogOpen(true);
  };

  const handleEdit = (id: string) => {
    console.log("Edit incident:", id);
    const incident = incidents.find(inc => inc.id === id);
    if (incident) {
      setSelectedIncident(incident);
      setIsActionDialogOpen(true);
    }
  };

  const handleDelete = (id: string) => {
    if (confirm("Are you sure you want to delete this incident?")) {
      deleteIncident(id);
    }
  };

  const handleAttachmentChange = (id: string, attachments: File[]) => {
    // Extract URLs from the file objects
    // For files that already have URLs (from previous uploads), use those
    // For new files, create new object URLs
    const attachmentUrls = attachments.map(file => {
      // If the file name is already a URL (from our previous implementation)
      if (file.name.startsWith('blob:') || file.name.startsWith('http')) {
        return file.name;
      }
      // Otherwise, create a new URL
      return URL.createObjectURL(file);
    });

    // Update the incident with the new attachments
    updateIncident(id, { attachments: attachmentUrls });
  };

  const handleInvestigation = (incidentId: string, currentStatus: string) => {
    // Find the incident and open the investigation status dialog
    const incident = incidents.find(inc => inc.id === incidentId);
    if (incident) {
      setSelectedIncident(incident);
      setIsInvestigationStatusDialogOpen(true);
    }
  };

  // Handle saving investigation assignment
  const handleInvestigationAssignment = (data: { leadInvestigator: string; remarks: string }) => {
    if (!selectedIncident) return;

    // Update the incident with lead investigator and remarks
    updateIncident(selectedIncident.id, {
      leadInvestigator: data.leadInvestigator,
      investigationRemarks: data.remarks,
      investigationStatus: 'in-progress' // Set status to in-progress when assigned
    });

    toast.success(`Investigation assigned for incident ${selectedIncident.id}`, {
      description: `Lead investigator assigned successfully`,
    });
  };

  return (
    <div className="w-full p-4 md:p-6 lg:p-8 animate-in fade-in duration-500">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-8">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">Reporter Dashboard</h1>
          <p className="text-muted-foreground mt-1">Report and track incidents as a safety officer</p>
        </div>
        <Button
          className="bg-primary hover:bg-primary/90 shadow-md transition-all hover:shadow-lg"
          size="lg"
          onClick={() => setIsNewIncidentDialogOpen(true)}
        >
          <Plus className="mr-2" />
          New Incident
        </Button>
      </div>

      {/* New Incident Dialog */}
      <NewIncidentDialog
        open={isNewIncidentDialogOpen}
        onOpenChange={setIsNewIncidentDialogOpen}
        onIncidentCreated={(newIncident) => {
          console.log("New incident created:", newIncident);
          // Add the new incident using the context
          addIncident(newIncident as Incident);
        }}
      />

      {/* Tabs and Data Tables with Search and Filter */}
      <div className="bg-card rounded-lg border shadow-sm overflow-hidden">
        <Tabs defaultValue="my-actions" className="w-full">
          <div className="bg-muted/30 border-b">
            <TabsList className="w-full justify-start h-14 bg-transparent p-0">
              <TabsTrigger value="my-actions" className="relative px-6 py-3 h-full rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-background data-[state=active]:shadow-none transition-all">
                My Actions
              </TabsTrigger>
              <TabsTrigger value="all-incidents" className="relative px-6 py-3 h-full rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-background data-[state=active]:shadow-none transition-all">
                All Incidents
              </TabsTrigger>
            </TabsList>
          </div>



        <TabsContent value="my-actions" className="animate-in fade-in-50 duration-300 p-6">
          {isLoadingActions ? (
            <div className="p-8 text-center border rounded-md">
              <h3 className="text-lg font-medium mb-2">Loading your action items...</h3>
              <p className="text-muted-foreground">Fetching incidents that require your attention.</p>
            </div>
          ) : reporterActionIncidents.length === 0 && myActionIncidents.length === 0 ? (
            <div className="p-8 text-center border rounded-md">
              <h3 className="text-lg font-medium mb-2">No Action Items</h3>
              <p className="text-muted-foreground">You have no incidents with "Reported" status that require action at this time.</p>
            </div>
          ) : (
            <DataTable
              data={reporterActionIncidents.length > 0 ? reporterActionIncidents : myActionIncidents}
              onView={handleView}
              onEdit={handleEdit}
              onDelete={handleDelete}
              context="my-actions"
            />
          )}
        </TabsContent>

        <TabsContent value="all-incidents" className="animate-in fade-in-50 duration-300 p-6">
          <AllIncidentsTable
            data={allIncidents}
            onView={handleView}
            onEdit={handleEdit}
            onDelete={handleDelete}
            onAttachmentChange={handleAttachmentChange}
            handleInvestigation={handleInvestigation}
            userRole="reporter"
          />
        </TabsContent>
      </Tabs>
      </div>

      {/* Incident Investigation View */}
      <IncidentInvestigationView
        open={isViewDialogOpen}
        onOpenChange={setIsViewDialogOpen}
        incident={selectedIncident}
      />

      {/* Incident Action Dialog */}
      <IncidentActionDialog
        open={isActionDialogOpen}
        onOpenChange={setIsActionDialogOpen}
        incident={selectedIncident}
        userRole="reporter"
        onActionComplete={(updatedIncident) => {
          console.log("Action completed in ReporterDashboard:", updatedIncident);
          // Use the updateIncident function from the context to update the incident
          updateIncident(updatedIncident.id, updatedIncident);
          // Clear the selected incident
          setSelectedIncident(null);
          // Close the dialog
          setIsActionDialogOpen(false);
        }}
      />

      {/* Investigation Status Dialog */}
      <InvestigationStatusDialog
        open={isInvestigationStatusDialogOpen}
        onOpenChange={setIsInvestigationStatusDialogOpen}
        incident={selectedIncident}
        onSave={handleInvestigationAssignment}
        onStartInvestigation={(data) => {
          // Handle starting comprehensive investigation
          console.log("Starting comprehensive investigation with data:", data);

          // Update the incident with lead investigator and start comprehensive investigation
          if (selectedIncident) {
            const updatedIncident = {
              ...selectedIncident,
              leadInvestigator: data.leadInvestigator,
              investigationRemarks: data.remarks,
              status: 'investigation' as const, // Change status to investigation
              investigationStatus: 'in-progress' as const,
              workflowStage: 'investigation' as const,
              stage: 'Investigation in Progress',
              requiresAction: false, // Remove from My Actions since it's now under investigation
              comprehensiveInvestigationStarted: true,
              comprehensiveInvestigationStartedAt: new Date(),
              comprehensiveInvestigationStartedBy: data.leadInvestigator,
            };

            // Update the incident in the context
            updateIncident(selectedIncident.id, updatedIncident);

            // Clear the selected incident
            setSelectedIncident(null);

            // Show success message
            toast.success("Comprehensive Investigation Started!", {
              description: `Lead investigator ${data.leadInvestigator} has been assigned and comprehensive investigation has started.`,
            });
          }
        }}
      />
    </div>
  );
};

export default ReporterDashboard;
