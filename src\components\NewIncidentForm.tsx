import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { format } from "date-fns";
import { toast } from "sonner";
import { CalendarIcon, Loader2 } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Checkbox } from "@/components/ui/checkbox";
import { cn } from "@/lib/utils";

import { newIncidentFormSchema, NewIncidentFormValues } from "@/utils/validationSchema";
import { ApiService } from "@/lib/utils";
import { useUser } from "@/contexts/UserContext";

interface NewIncidentFormProps {
  onSubmit: (data: NewIncidentFormValues) => void;
  onCancel: () => void;
}

interface LocationOption {
  id: string;
  name: string;
}

const NewIncidentForm: React.FC<NewIncidentFormProps> = ({ onSubmit, onCancel }) => {
  const { userData } = useUser();
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Location state
  const [locationOnes, setLocationOnes] = useState<LocationOption[]>([]);
  const [locationTwos, setLocationTwos] = useState<LocationOption[]>([]);
  const [locationThrees, setLocationThrees] = useState<LocationOption[]>([]);
  const [locationFours, setLocationFours] = useState<LocationOption[]>([]);
  const [locationFives, setLocationFives] = useState<LocationOption[]>([]);
  const [locationSixes, setLocationSixes] = useState<LocationOption[]>([]);

  // Incident categories state
  const [incidentCategories, setIncidentCategories] = useState<LocationOption[]>([]);

  const [loadingLocations, setLoadingLocations] = useState({
    ones: false,
    twos: false,
    threes: false,
    fours: false,
    fives: false,
    sixes: false,
  });

  const form = useForm<NewIncidentFormValues>({
    resolver: zodResolver(newIncidentFormSchema),
    defaultValues: {
      title: "",
      description: "",
      incidentDate: "",
      date: "",
      isWorkRelated: false,
      isInjury: false,
      isFirstAid: false,
      isPersonInjured: false,
      isMedicalTreatment: false,
      isControlMeasure: false,
      isRiskAssessment: false,
      isMedicalLeave: false,
      investigationStatus: false,
      triggerInvestigationStatus: false,
      active: true,
      confirmAccuracy: false,
      userId: userData?.id || "",
    },
  });

  // Load location ones and incident categories on component mount
  useEffect(() => {
    loadLocationOnes();
    loadIncidentCategories();
  }, []);

  const loadIncidentCategories = async () => {
    try {
      const response = await ApiService.getIncidentCircumstanceCategories();
      setIncidentCategories(response || []);
    } catch (error) {
      console.error("Failed to load incident categories:", error);
      toast.error("Failed to load incident categories");
    }
  };

  const loadLocationOnes = async () => {
    setLoadingLocations(prev => ({ ...prev, ones: true }));
    try {
      const response = await ApiService.getLocationOnes();
      setLocationOnes(response.data || []);
    } catch (error) {
      console.error("Failed to load location ones:", error);
      toast.error("Failed to load locations");
    } finally {
      setLoadingLocations(prev => ({ ...prev, ones: false }));
    }
  };

  const loadLocationTwos = async (locationOneId: string) => {
    setLoadingLocations(prev => ({ ...prev, twos: true }));
    try {
      const response = await ApiService.getLocationTwos(locationOneId);
      setLocationTwos(response.data || []);
      // Reset dependent locations
      setLocationThrees([]);
      setLocationFours([]);
      setLocationFives([]);
      setLocationSixes([]);
    } catch (error) {
      console.error("Failed to load location twos:", error);
      toast.error("Failed to load sub-locations");
    } finally {
      setLoadingLocations(prev => ({ ...prev, twos: false }));
    }
  };

  const loadLocationThrees = async (locationTwoId: string) => {
    setLoadingLocations(prev => ({ ...prev, threes: true }));
    try {
      const response = await ApiService.getLocationThrees(locationTwoId);
      setLocationThrees(response.data || []);
      // Reset dependent locations
      setLocationFours([]);
      setLocationFives([]);
      setLocationSixes([]);
    } catch (error) {
      console.error("Failed to load location threes:", error);
      toast.error("Failed to load sub-locations");
    } finally {
      setLoadingLocations(prev => ({ ...prev, threes: false }));
    }
  };

  const loadLocationFours = async (locationThreeId: string) => {
    setLoadingLocations(prev => ({ ...prev, fours: true }));
    try {
      const response = await ApiService.getLocationFours(locationThreeId);
      setLocationFours(response.data || []);
      // Reset dependent locations
      setLocationFives([]);
      setLocationSixes([]);
    } catch (error) {
      console.error("Failed to load location fours:", error);
      toast.error("Failed to load sub-locations");
    } finally {
      setLoadingLocations(prev => ({ ...prev, fours: false }));
    }
  };

  const loadLocationFives = async (locationFourId: string) => {
    setLoadingLocations(prev => ({ ...prev, fives: true }));
    try {
      const response = await ApiService.getLocationFives(locationFourId);
      setLocationFives(response.data || []);
      // Reset dependent locations
      setLocationSixes([]);
    } catch (error) {
      console.error("Failed to load location fives:", error);
      toast.error("Failed to load sub-locations");
    } finally {
      setLoadingLocations(prev => ({ ...prev, fives: false }));
    }
  };

  const loadLocationSixes = async (locationFiveId: string) => {
    setLoadingLocations(prev => ({ ...prev, sixes: true }));
    try {
      const response = await ApiService.getLocationSixes(locationFiveId);
      setLocationSixes(response.data || []);
    } catch (error) {
      console.error("Failed to load location sixes:", error);
      toast.error("Failed to load sub-locations");
    } finally {
      setLoadingLocations(prev => ({ ...prev, sixes: false }));
    }
  };

  const handleSubmit = async (data: NewIncidentFormValues) => {
    setIsSubmitting(true);
    try {
      // Format the data for the API
      const formattedData = {
        ...data,
        date: new Date().toISOString(),
        incidentDate: data.incidentDate,
        userId: userData?.id || "",
        incidentCircumstanceCategoryId: data.IncidentCategory || "",
      };

      await ApiService.createIncident(formattedData);
      toast.success("Incident reported successfully");
      onSubmit(data);
    } catch (error) {
      console.error("Failed to create incident:", error);
      toast.error("Failed to report incident. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900">Report New Incident</h2>
        <p className="text-gray-600 mt-2">Please provide detailed information about the incident</p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          {/* Basic Information */}
          <div className="bg-white p-6 rounded-lg border space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Basic Information</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Incident Title *</FormLabel>
                    <FormControl>
                      <Input placeholder="Brief description of the incident" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="incidentDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Incident Date *</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(new Date(field.value), "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value ? new Date(field.value) : undefined}
                          onSelect={(date) => field.onChange(date?.toISOString().split('T')[0])}
                          disabled={(date) => date > new Date() || date < new Date("1900-01-01")}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description *</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Provide a detailed description of what happened"
                      className="min-h-[100px]"
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Location Hierarchy */}
          <div className="bg-white p-6 rounded-lg border space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Location Information</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {/* Location One */}
              <FormField
                control={form.control}
                name="locationOneId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Primary Location</FormLabel>
                    <Select
                      onValueChange={(value) => {
                        field.onChange(value);
                        if (value) {
                          loadLocationTwos(value);
                        }
                      }}
                      value={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={loadingLocations.ones ? "Loading..." : "Select location"} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {locationOnes.map((location) => (
                          <SelectItem key={location.id} value={location.id}>
                            {location.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Location Two */}
              <FormField
                control={form.control}
                name="locationTwoId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Sub Location</FormLabel>
                    <Select
                      onValueChange={(value) => {
                        field.onChange(value);
                        if (value) {
                          loadLocationThrees(value);
                        }
                      }}
                      value={field.value}
                      disabled={!form.watch("locationOneId") || loadingLocations.twos}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={loadingLocations.twos ? "Loading..." : "Select sub-location"} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {locationTwos.map((location) => (
                          <SelectItem key={location.id} value={location.id}>
                            {location.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Location Three */}
              <FormField
                control={form.control}
                name="locationThreeId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Detailed Location</FormLabel>
                    <Select
                      onValueChange={(value) => {
                        field.onChange(value);
                        if (value) {
                          loadLocationFours(value);
                        }
                      }}
                      value={field.value}
                      disabled={!form.watch("locationTwoId") || loadingLocations.threes}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={loadingLocations.threes ? "Loading..." : "Select detailed location"} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {locationThrees.map((location) => (
                          <SelectItem key={location.id} value={location.id}>
                            {location.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* Incident Classification */}
          <div className="bg-white p-6 rounded-lg border space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Incident Classification</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Work Related */}
              <FormField
                control={form.control}
                name="isWorkRelated"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Work Related Incident</FormLabel>
                    </div>
                  </FormItem>
                )}
              />

              {/* Injury */}
              <FormField
                control={form.control}
                name="isInjury"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Injury Occurred</FormLabel>
                    </div>
                  </FormItem>
                )}
              />

              {/* Person Injured */}
              <FormField
                control={form.control}
                name="isPersonInjured"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Person Injured</FormLabel>
                    </div>
                  </FormItem>
                )}
              />

              {/* First Aid */}
              <FormField
                control={form.control}
                name="isFirstAid"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>First Aid Required</FormLabel>
                    </div>
                  </FormItem>
                )}
              />

              {/* Medical Treatment */}
              <FormField
                control={form.control}
                name="isMedicalTreatment"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Medical Treatment Required</FormLabel>
                    </div>
                  </FormItem>
                )}
              />

              {/* Medical Leave */}
              <FormField
                control={form.control}
                name="isMedicalLeave"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Medical Leave Required</FormLabel>
                    </div>
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* Additional Information */}
          <div className="bg-white p-6 rounded-lg border space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Additional Information</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="IncidentCategory"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Incident Category</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select incident category" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {incidentCategories
                          .filter((category) => category.name !== "Environmental")
                          .map((category) => (
                            <SelectItem key={category.id} value={category.id}>
                              {category.name}
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="actualImpact"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Actual Impact</FormLabel>
                    <FormControl>
                      <Input placeholder="Describe the actual impact" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="potentialImpact"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Potential Impact</FormLabel>
                    <FormControl>
                      <Input placeholder="Describe the potential impact" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="propertyDamage"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Property Damage</FormLabel>
                    <FormControl>
                      <Input placeholder="Describe any property damage" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="dangerousOccurance"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Dangerous Occurrence</FormLabel>
                    <FormControl>
                      <Input placeholder="Describe dangerous occurrence" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="fatality"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Fatality Details</FormLabel>
                    <FormControl>
                      <Input placeholder="Fatality information if applicable" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="injury"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Injury Details</FormLabel>
                    <FormControl>
                      <Input placeholder="Describe injury details" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="lostTime"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Lost Time</FormLabel>
                    <FormControl>
                      <Input placeholder="Lost time information" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="medicalTreatment"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Medical Treatment</FormLabel>
                    <FormControl>
                      <Input placeholder="Medical treatment details" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="firstAid"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>First Aid Details</FormLabel>
                    <FormControl>
                      <Input placeholder="First aid provided" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="lossOfConscious"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Loss of Consciousness</FormLabel>
                    <FormControl>
                      <Input placeholder="Loss of consciousness details" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="maskId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Mask ID</FormLabel>
                    <FormControl>
                      <Input placeholder="Mask identification" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Property Damage Details */}
            <FormField
              control={form.control}
              name="propertyDamageDetails"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Property Damage Details</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Provide detailed information about property damage"
                      className="min-h-[80px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Work Related Details */}
            <FormField
              control={form.control}
              name="workRelatedDetails"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Work Related Details</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Provide details about work-related aspects"
                      className="min-h-[80px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Authority and Reporting */}
          <div className="bg-white p-6 rounded-lg border space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Authority and Reporting</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="reportAuthority"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Report to Authority</FormLabel>
                    <FormControl>
                      <Input placeholder="Authority to report to" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="authorityName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Authority Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Name of the authority" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="stopWorkOrder"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Stop Work Order</FormLabel>
                    <FormControl>
                      <Input placeholder="Stop work order details" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="classification"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Classification</FormLabel>
                    <FormControl>
                      <Input placeholder="Incident classification" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* Authority and Reporting */}
          <div className="bg-white p-6 rounded-lg border space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Authority and Reporting</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="reportAuthority"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Report to Authority</FormLabel>
                    <FormControl>
                      <Input placeholder="Authority to report to" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="authorityName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Authority Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Name of the authority" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="stopWorkOrder"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Stop Work Order</FormLabel>
                    <FormControl>
                      <Input placeholder="Stop work order details" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="classification"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Classification</FormLabel>
                    <FormControl>
                      <Input placeholder="Incident classification" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* Investigation and Management */}
          <div className="bg-white p-6 rounded-lg border space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Investigation and Management</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="incidentOwner"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Incident Owner</FormLabel>
                    <FormControl>
                      <Input placeholder="Incident owner name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status</FormLabel>
                    <FormControl>
                      <Input placeholder="Current status" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="informationStep"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Information Step</FormLabel>
                    <FormControl>
                      <Input placeholder="Information gathering step" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="investigationStep"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Investigation Step</FormLabel>
                    <FormControl>
                      <Input placeholder="Investigation step" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Investigation Remarks */}
            <FormField
              control={form.control}
              name="investigationRemarks"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Investigation Remarks</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Investigation remarks and findings"
                      className="min-h-[80px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Risk Control */}
            <FormField
              control={form.control}
              name="riskControl"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Risk Control</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Risk control measures"
                      className="min-h-[80px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Environmental Conditions */}
          <div className="bg-white p-6 rounded-lg border space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Environmental Conditions</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="lightingId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Lighting Conditions</FormLabel>
                    <FormControl>
                      <Input placeholder="Lighting condition ID" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="surfaceTypeId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Surface Type</FormLabel>
                    <FormControl>
                      <Input placeholder="Surface type ID" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="surfaceConditionId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Surface Condition</FormLabel>
                    <FormControl>
                      <Input placeholder="Surface condition ID" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="weatherConditionId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Weather Condition</FormLabel>
                    <FormControl>
                      <Input placeholder="Weather condition ID" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="workActivityId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Work Activity</FormLabel>
                    <FormControl>
                      <Input placeholder="Work activity ID" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="riskCategoryId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Risk Category</FormLabel>
                    <FormControl>
                      <Input placeholder="Risk category ID" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* Confirmation */}
          <div className="bg-white p-6 rounded-lg border">
            <FormField
              control={form.control}
              name="confirmAccuracy"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                      I confirm that the information provided is accurate to the best of my knowledge *
                    </FormLabel>
                  </div>
                </FormItem>
              )}
            />
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-4 pt-6 border-t">
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isSubmitting ? "Submitting..." : "Submit Incident Report"}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default NewIncidentForm;
