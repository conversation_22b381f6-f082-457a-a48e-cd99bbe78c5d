
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { ThemeProvider } from "next-themes";
import SimpleLayout from "./components/layout/SimpleLayout";
import Index from "./pages/Index";
import Incidents from "./pages/Incidents";
import NotFound from "./pages/NotFound";
import ReporterDashboard from "./pages/ReporterDashboard";
import ReviewerDashboard from "./pages/ReviewerDashboard";
import ApiTestPage from "./pages/ApiTestPage";

import { UserProvider, useUser } from "./contexts/UserContext";
import { IncidentProvider } from "./contexts/IncidentContext";
import { defaultPath } from "./lib/paths";
import "./App.css";

const queryClient = new QueryClient();

// Component to handle role-based redirection
const RoleBasedRedirect = () => {
  const { availableRoles, hasIncidentReporter, hasIncidentReviewer, isLoading } = useUser();

  // Show loading while fetching user data
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading user data...</p>
        </div>
      </div>
    );
  }

  // Redirect based on available roles
  if (availableRoles.length === 1) {
    // User has only one role - redirect directly
    if (hasIncidentReporter) {
      return <Navigate to={defaultPath.reporter} replace />;
    }
    if (hasIncidentReviewer) {
      return <Navigate to={defaultPath.reviewer} replace />;
    }
  }

  // User has multiple roles - prioritize reviewer
  if (hasIncidentReviewer) {
    return <Navigate to={defaultPath.reviewer} replace />;
  }
  if (hasIncidentReporter) {
    return <Navigate to={defaultPath.reporter} replace />;
  }

  // Fallback - no incident roles, redirect to reporter dashboard
  return <Navigate to={defaultPath.reporter} replace />;
};

const App = () => (
  <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
    <QueryClientProvider client={queryClient}>
      <UserProvider>
        <IncidentProvider>
          <TooltipProvider>
            <Toaster />
            <Sonner />
            <BrowserRouter>
              <SimpleLayout>
                <Routes>
                  {/* Role-based redirect for root path */}
                  <Route path="/" element={<RoleBasedRedirect />} />

                  {/* Role-specific dashboards */}
                  <Route path={defaultPath.reporter} element={<ReporterDashboard />} />
                  <Route path={defaultPath.reviewer} element={<ReviewerDashboard />} />

                  {/* Legacy routes */}
                  <Route path={defaultPath.incidents} element={<Incidents />} />
                  <Route path={defaultPath.report} element={<Index />} />

                  {/* API Test route */}
                  <Route path="/api-test" element={<ApiTestPage />} />

                  {/* Catch-all route */}
                  <Route path="*" element={<NotFound />} />
                </Routes>
              </SimpleLayout>
            </BrowserRouter>
          </TooltipProvider>
        </IncidentProvider>
      </UserProvider>
    </QueryClientProvider>
  </ThemeProvider>
);

export default App;
