import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { ApiService } from '@/lib/utils';

// Define the available user roles
export type UserRole = 'reporter' | 'reviewer';

// Define the validation role interface
interface ValidationRole {
  id: string;
  name: string;
}

// Define the user data interface
interface UserData {
  id?: string;
  username?: string;
  email?: string;
  name?: string;
  groups?: string[];
  validationRoles?: ValidationRole[];
  [key: string]: any;
}

// Define the user context type
interface UserContextType {
  role: UserRole;
  setRole: (role: UserRole) => void;
  userName: string;
  userTitle: string;
  userData: UserData | null;
  isLoading: boolean;
  error: string | null;
  fetchUserData: () => Promise<void>;
  availableRoles: UserRole[];
  hasIncidentReporter: boolean;
  hasIncidentReviewer: boolean;
}

// Create the context with default values
const UserContext = createContext<UserContextType>({
  role: 'reporter',
  setRole: () => {},
  userName: 'Safety Reporter',
  userTitle: 'Safety Officer',
  userData: null,
  isLoading: false,
  error: null,
  fetchUserData: async () => {},
  availableRoles: [],
  hasIncidentReporter: false,
  hasIncidentReviewer: false,
});

// Create a provider component
interface UserProviderProps {
  children: ReactNode;
}

export const UserProvider: React.FC<UserProviderProps> = ({ children }) => {
  const [role, setRole] = useState<UserRole>('reporter');
  const [userData, setUserData] = useState<UserData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Analyze user's validation roles to determine available roles
  const analyzeValidationRoles = (validationRoles: ValidationRole[] = []) => {
    const hasIncidentReporter = validationRoles.some(role => role.name === 'Incident Reporter');
    const hasIncidentReviewer = validationRoles.some(role => role.name === 'Incident Reviewer');

    const availableRoles: UserRole[] = [];
    if (hasIncidentReporter) availableRoles.push('reporter');
    if (hasIncidentReviewer) availableRoles.push('reviewer');

    return { hasIncidentReporter, hasIncidentReviewer, availableRoles };
  };

  const { hasIncidentReporter, hasIncidentReviewer, availableRoles } = analyzeValidationRoles(userData?.validationRoles);

  // Get user title based on role
  const getUserTitle = (userRole: UserRole): string => {
    switch (userRole) {
      case 'reporter':
        return 'Safety Officer';
      case 'reviewer':
        return 'Safety Manager';
      default:
        return 'Employee';
    }
  };

  // Get user name based on role or from API data
  const getUserName = (userRole: UserRole): string => {
    // If we have user data from API, use that
    if (userData?.name) {
      return userData.name;
    }
    if (userData?.username) {
      return userData.username;
    }

    // Fallback to role-based names
    switch (userRole) {
      case 'reporter':
        return 'Safety Reporter';
      case 'reviewer':
        return 'Safety Reviewer';
      default:
        return 'User';
    }
  };

  // Function to fetch user data from API
  const fetchUserData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('Fetching user data from API...');
      const data = await ApiService.getCurrentUser();
      console.log('User data received:', data);
      setUserData(data);

      // Auto-set role based on available roles
      if (data?.validationRoles) {
        const roleAnalysis = analyzeValidationRoles(data.validationRoles);
        console.log('Role analysis:', roleAnalysis);

        // Set default role based on available roles
        if (roleAnalysis.availableRoles.length === 1) {
          setRole(roleAnalysis.availableRoles[0]);
        } else if (roleAnalysis.availableRoles.length > 1) {
          // If user has both roles, prioritize reviewer
          if (roleAnalysis.hasIncidentReviewer) {
            setRole('reviewer');
          } else {
            setRole('reporter');
          }
        }
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch user data';
      console.error('Error fetching user data:', errorMessage);
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch user data on component mount
  useEffect(() => {
    fetchUserData();
  }, []);

  const userName = getUserName(role);
  const userTitle = getUserTitle(role);

  return (
    <UserContext.Provider value={{
      role,
      setRole,
      userName,
      userTitle,
      userData,
      isLoading,
      error,
      fetchUserData,
      availableRoles,
      hasIncidentReporter,
      hasIncidentReviewer
    }}>
      {children}
    </UserContext.Provider>
  );
};

// Create a hook to use the user context
export const useUser = () => useContext(UserContext);
